package com.glory.mes.pvc.ejb;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.ejb.EJB;
import javax.ejb.Local;
import javax.ejb.Remote;
import javax.ejb.Stateless;
import javax.interceptor.Interceptors;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;

import com.glory.framework.activeentity.model.ADBase;
import com.glory.framework.core.exception.ClientException;
import com.glory.framework.core.exception.ExceptionManager;
import com.glory.framework.core.util.SessionContext;
import com.glory.framework.tenant.interceptor.TenantEJBInterceptor;
import com.glory.mes.pp.ejb.PpManagerLocal;
import com.glory.mes.pp.model.WorkOrder;
import com.glory.mes.pvc.client.PvcPpManager;
import com.glory.mes.pvc.model.LineConfig;
import com.glory.mes.pvc.model.LineWorkOrder;
import com.glory.mes.wip.his.WorkOrderHis;
import com.glory.mes.wip.model.Exceptions;
import org.apache.commons.lang.StringUtils;

@Stateless
@Remote(PvcPpManager.class)
@Local(PvcPpManagerLocal.class)
@Interceptors({TenantEJBInterceptor.class})
public class PvcPpManagerBean implements PvcPpManager, PvcPpManagerLocal {
	
	private static final Logger logger = Logger.getLogger(PvcPpManagerBean.class);

    @PersistenceContext
    private EntityManager em;

    @EJB(lookup="java:global/MESwell/wip/PpManagerBean!com.glory.mes.pp.ejb.PpManagerLocal")
    private PpManagerLocal ppManager;

	/**
	 * 工单投料 生成批次时再增加工单投入数量
	 * @param workOrder 投入的工单
	 * @param inputQty 投入的数量
	 * @param sc 会话上下文
	 * @return WorkOrder
	 */
	@Override
	public WorkOrder startWorkOrder(WorkOrder workOrder, BigDecimal inputQty, SessionContext sc) {
		sc.buildTransInfo();
		try {
			if (!(WorkOrder.STATUS_APPROVED.equals(workOrder.getDocStatus())
					|| WorkOrder.STATUS_STARTED.equals(workOrder.getDocStatus()))) {
				throw new ClientException("wip.wo_state_not_allowed");
			}

			workOrder.setUpdated(new Date());
			workOrder.setUpdatedBy(sc.getUserName());
			workOrder.setDocStatus(WorkOrder.STATUS_STARTED);
			workOrder = em.merge(workOrder);

			WorkOrderHis his = new WorkOrderHis(workOrder);
			his.setTransType(WorkOrderHis.TRANSTYPE_START);
			his.setCreatedBy(sc.getUserName());
			his.setUpdatedBy(sc.getUserName());
			em.persist(his);
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return workOrder;
	}
	
	/**
	 * 根据上报包装数量增加工单完成数量
	 * 完成数量到达工单总数量关闭工单
	 * @param workOrder	工单
	 * @param mainQty 包装数量
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	@Override
	public void completeWorkOrder(WorkOrder workOrder, BigDecimal mainQty, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			if (WorkOrder.STATUS_COMPLETED.equals(workOrder.getDocStatus())) {
				throw new ClientException("wip.wo_state_not_allowed");
			}
			workOrder = em.find(WorkOrder.class, workOrder.getObjectRrn());
			
			workOrder.setUpdated(new Date());
			workOrder.setUpdatedBy(sc.getUserName());
			
			if (workOrder.getCompleteMainQty() == null) {
				workOrder.setCompleteMainQty(mainQty);
			} else {
				workOrder.setCompleteMainQty(workOrder.getCompleteMainQty().add(mainQty));
			}
			
			if (workOrder.getCompleteMainQty().compareTo(workOrder.getMainQty()) >= 0) {
				ppManager.completedWorkOrder(workOrder, sc);
			} else {
				em.merge(workOrder);
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(e);
		}
	}
	
	/**
	 * 根据数量扣减工单包装数量
	 * @param workOrder	工单
	 * @param mainQty 包装数量
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	@Override
	public void lessWorkOrderCompleteQty(WorkOrder workOrder, BigDecimal mainQty, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			if (WorkOrder.STATUS_COMPLETED.equals(workOrder.getDocStatus())) {
				throw new ClientException("wip.wo_state_not_allowed");
			}
			
			workOrder.setUpdated(new Date());
			workOrder.setUpdatedBy(sc.getUserName());
			
			if (workOrder.getCompleteMainQty() == null) {
				workOrder.setCompleteMainQty(BigDecimal.ZERO);
			} else if (workOrder.getCompleteMainQty().compareTo(mainQty) >= 0) {
				workOrder.setCompleteMainQty(workOrder.getCompleteMainQty().subtract(mainQty));
			}
			
			em.merge(workOrder);
		} catch (Exception e) {
			throw ExceptionManager.handleException(e);
		}
	}
	
	/**
	 * 根据数量增加工单发料数量
	 * @param workOrder	工单
	 * @param mainQty 发料数量
	 * @param sc 会话上下文
	 * @throws ClientException
	 */
	@Override
	public void increaseWorkOrderStartQty(WorkOrder workOrder, BigDecimal mainQty, SessionContext sc) throws ClientException {
		try {
			sc.buildTransInfo();
			if (WorkOrder.STATUS_COMPLETED.equals(workOrder.getDocStatus())) {
				throw new ClientException("wip.wo_state_not_allowed");
			}
			
			workOrder.setUpdated(new Date());
			workOrder.setUpdatedBy(sc.getUserName());
			
			if (workOrder.getStartedMainQty() == null) {
				workOrder.setStartedMainQty(BigDecimal.ZERO);
			} else {
				workOrder.setStartedMainQty(workOrder.getStartedMainQty().add(mainQty));
			}
			
			em.merge(workOrder);
		} catch (Exception e) {
			throw ExceptionManager.handleException(e);
		}
	}
	
	/**
	 * 根据线别找到对应工单
	 * @param lineId	线别工单
	 * @param sc	会话上下文
	 * @return
	 */
	@SuppressWarnings("unchecked")
	@Override
	public WorkOrder getWorkOrderByLine(String lineId, SessionContext sc) {
		try {
			// 从PVC_LINE_CONFIG表的RESERVED02字段获取工单ID
			StringBuffer sql = new StringBuffer(" SELECT LineConfig FROM LineConfig LineConfig ");
			sql.append(" WHERE ");
			sql.append(ADBase.BASE_CONDITION_N);
			sql.append(" AND lineId = :lineId ");

			Query query = em.createQuery(sql.toString());
			query.setParameter("orgRrn", sc.getOrgRrn());
			query.setParameter("lineId", lineId);

			List<LineConfig> lineConfigs = query.getResultList();
			if (CollectionUtils.isEmpty(lineConfigs)) {
				logger.warn("根据线别[" + lineId + "]未找到对应的线别配置信息");
				throw new ClientException("pp.wo_is_not_exist");
			}

			LineConfig lineConfig = lineConfigs.get(0);
			String woId = lineConfig.getReserved02();

			if (StringUtils.isEmpty(woId)) {
				logger.warn("线别[" + lineId + "]配置中的工单ID(RESERVED02)为空");
				throw new ClientException("pp.wo_is_not_exist");
			}

			// 根据工单ID查询工单详细信息
			WorkOrder wo = getWorkOrderByDocId(woId, true, sc);
			logger.info("根据线别[" + lineId + "]成功获取工单[" + woId + "]");
			return wo;

		} catch (Exception e) {
			logger.error("根据线别[" + lineId + "]查询工单失败: " + e.getMessage(), e);
			throw ExceptionManager.handleException(e);
		}
	}
	
	/**
	 * 根据工单ID查询工单
	 * @param docId	工单id
	 * @param isThrowException	是否抛出异常
	 * @param sc 会话上下文
	 * @return
	 * @throws ClientException
	 */
	@SuppressWarnings("unchecked")
	@Override
	public WorkOrder getWorkOrderByDocId(String docId, boolean isThrowException, SessionContext sc) throws ClientException {
		try {
			StringBuffer buffer = new StringBuffer(" SELECT WorkOrder FROM  WorkOrder WorkOrder ");
			buffer.append(" WHERE ");
			buffer.append(ADBase.BASE_CONDITION_N);
			buffer.append(" AND docId = :docId ");
			
			Query query = em.createQuery(buffer.toString());
			query.setParameter("orgRrn", sc.getOrgRrn());
			query.setParameter("docId", docId);
			List<WorkOrder> workOrders = query.getResultList();
			if (workOrders != null && workOrders.size() > 0) {
				return workOrders.get(0);
			} else {
				if (isThrowException) {
					throw new ClientException(Exceptions.WO_IS_NOT_EXIST);
				}
			}
		} catch (Exception e) {
			throw ExceptionManager.handleException(logger, e);
		}
		return null;
	}

}
